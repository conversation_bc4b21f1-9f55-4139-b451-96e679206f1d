using _Cashdisbursment_.Models;
using _Cashdisbursment_.Services;
using _Cashdisbursment_.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace _Cashdisbursment_.Pages.Admin
{
    public class ApproversModel : PageModel
    {
        private readonly ApprovalService _approvalService;
        private readonly UserService _userService;

        public ApproversModel(ApprovalService approvalService, UserService userService)
        {
            _approvalService = approvalService;
            _userService = userService;
        }

        public User? CurrentUser { get; set; }
        public List<ApprovalList> Approvers { get; set; } = new();
        public List<User> AvailableUsers { get; set; } = new();

        [TempData]
        public string? SuccessMessage { get; set; }

        [TempData]
        public string? ErrorMessage { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);

            if (CurrentUser == null || !AuthUtility.CanManageApprovers(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            Approvers = await _approvalService.GetAllApproversAsync();
            AvailableUsers = await _userService.GetAdminAndApproverUsersAsync();

            return Page();
        }

        public async Task<IActionResult> OnPostAddAsync(int userId, int approverNum)
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);

            if (CurrentUser == null || !AuthUtility.IsAdmin(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            try
            {
                // Validate input
                if (userId <= 0 || approverNum < 1)
                {
                    ErrorMessage = "Please select a valid user and approval level.";
                    return RedirectToPage();
                }

                // Get the user
                var user = await _userService.GetUserByIdAsync(userId);
                if (user == null)
                {
                    ErrorMessage = "Selected user not found.";
                    return RedirectToPage();
                }

                // Check if user is already an approver
                if (await _approvalService.EmailExistsAsync(user.Email))
                {
                    ErrorMessage = "This user is already assigned as an approver.";
                    return RedirectToPage();
                }

                // Check if approval level already exists
                if (await _approvalService.ApproverNumExistsAsync(approverNum))
                {
                    ErrorMessage = $"Approval level {approverNum} is already assigned to another approver.";
                    return RedirectToPage();
                }

                var approver = new ApprovalList
                {
                    Email = user.Email.Trim().ToLower(),
                    ApproverNum = approverNum
                };

                var success = await _approvalService.AddApproverAsync(approver);

                if (success)
                {
                    SuccessMessage = $"User {user.Email} has been assigned as Level {approverNum} approver.";
                }
                else
                {
                    ErrorMessage = "Failed to add approver. Please try again.";
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = "An error occurred while adding the approver. Please try again.";
                // Log the exception in a real application
            }

            return RedirectToPage();
        }

        public async Task<IActionResult> OnPostEditAsync(int id, string email, int approverNum)
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);
            
            if (CurrentUser == null || !AuthUtility.IsAdmin(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            try
            {
                // Validate input
                if (string.IsNullOrWhiteSpace(email) || approverNum < 1)
                {
                    ErrorMessage = "Please provide valid email and approval level.";
                    return RedirectToPage();
                }

                // Check if email already exists (excluding current approver)
                if (await _approvalService.EmailExistsAsync(email, id))
                {
                    ErrorMessage = "An approver with this email already exists.";
                    return RedirectToPage();
                }

                // Check if approval level already exists (excluding current approver)
                if (await _approvalService.ApproverNumExistsAsync(approverNum, id))
                {
                    ErrorMessage = $"Approval level {approverNum} is already assigned to another approver.";
                    return RedirectToPage();
                }

                var approver = new ApprovalList
                {
                    Id = id,
                    Email = email.Trim().ToLower(),
                    ApproverNum = approverNum
                };

                var success = await _approvalService.UpdateApproverAsync(approver);

                if (success)
                {
                    SuccessMessage = "Approver updated successfully.";
                }
                else
                {
                    ErrorMessage = "Failed to update approver. Please try again.";
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = "An error occurred while updating the approver. Please try again.";
                // Log the exception in a real application
            }

            return RedirectToPage();
        }

        public async Task<IActionResult> OnPostDeleteAsync(int id)
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);
            
            if (CurrentUser == null || !AuthUtility.IsAdmin(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            try
            {
                var success = await _approvalService.DeleteApproverAsync(id);

                if (success)
                {
                    SuccessMessage = "Approver removed successfully.";
                }
                else
                {
                    ErrorMessage = "Failed to remove approver. Please try again.";
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = "An error occurred while removing the approver. Please try again.";
                // Log the exception in a real application
            }

            return RedirectToPage();
        }
    }
}
