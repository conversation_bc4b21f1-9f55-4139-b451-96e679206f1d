@page
@model _Cashdisbursment_.Pages.Admin.ApplicationsModel
@using _Cashdisbursment_.Utilities
@{
    ViewData["Title"] = "Manage Applications";
    var canApprove = AuthUtility.CanApprove(HttpContext.Session);
    var isAdmin = AuthUtility.IsAdmin(HttpContext.Session);
}

<div class="container">
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="h3 mb-1">Manage Applications</h1>
                <p class="text-muted mb-0">Review and process fund applications</p>
            </div>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(Model.SuccessMessage))
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>@Model.SuccessMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>@Model.ErrorMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <div class="py-section">
        <!-- Filter and Search -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form method="get" class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label">Search</label>
                                <input type="text" name="SearchTerm" value="@Model.SearchTerm" class="form-control"
                                       placeholder="Search by company or description...">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Status</label>
                                <select name="StatusFilter" class="form-select">
                                    <option value="">All Status</option>
                                    <option value="pending" selected="@(Model.StatusFilter == "pending")">Pending</option>
                                    <option value="approved" selected="@(Model.StatusFilter == "approved")">Approved</option>
                                    <option value="disbursed" selected="@(Model.StatusFilter == "disbursed")">Disbursed</option>
                                    <option value="rejected" selected="@(Model.StatusFilter == "rejected")">Rejected</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Company</label>
                                <select name="CompanyFilter" class="form-select">
                                    <option value="">All Companies</option>
                                    @foreach (var company in Model.Companies)
                                    {
                                        <option value="@company.CompanyID" selected="@(Model.CompanyFilter == company.CompanyID)">
                                            @company.Name
                                        </option>
                                    }
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Date Range</label>
                                <select name="DateRange" class="form-select">
                                    <option value="">All Time</option>
                                    <option value="7" selected="@(Model.DateRange == "7")">Last 7 days</option>
                                    <option value="30" selected="@(Model.DateRange == "30")">Last 30 days</option>
                                    <option value="90" selected="@(Model.DateRange == "90")">Last 90 days</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-outline-primary">
                                        <i class="fas fa-search me-1"></i>Filter
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

       

        <!-- Applications Accordion -->
        <div class="row">
            <div class="col-12">
                @{
                    var pendingApps = Model.Applications.Where(a => a.Status == "Pending" && !a.IsDisbursed).ToList();
                    var approvedApps = Model.Applications.Where(a => a.Status == "Approved").ToList();
                    var rejectedApps = Model.Applications.Where(a => a.Status == "Rejected").ToList();
                }

                <div class="accordion applications-accordion" id="applicationsAccordion">
                    <!-- Pending Applications -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingPending">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapsePending" aria-expanded="true" aria-controls="collapsePending">
                                <i class="fas fa-clock me-2 text-warning"></i>
                                Pending Applications
                                <span class="badge bg-warning ms-2">@pendingApps.Count</span>
                            </button>
                        </h2>
                        <div id="collapsePending" class="accordion-collapse collapse show" aria-labelledby="headingPending" data-bs-parent="#applicationsAccordion">
                            <div class="accordion-body">
                                @if (pendingApps.Any())
                                {
                                    <div class="table-container">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>ID</th>
                                                    <th>Company</th>
                                                    <th>Description</th>
                                                    <th>Amount</th>
                                                    <th>Date</th>
                                                    <th>Level</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach (var app in pendingApps)
                                                {
                                                    <tr>
                                                        <td><strong>#@app.ApplicationID</strong></td>
                                                        <td>
                                                            <div class="fw-bold">@app.Company?.Name</div>
                                                        </td>
                                                        <td>
                                                            <div class="text-truncate" style="max-width: 200px;" title="@app.Description">
                                                                @app.Description
                                                            </div>
                                                        </td>
                                                        <td><strong>$@app.RequestedCash.ToString("N2")</strong></td>
                                                        <td>@app.DateRequested.ToString("MMM dd, yyyy")</td>
                                                        <td>
                                                            @if (app.ApprovalLevel > 0)
                                                            {
                                                                <span class="badge bg-info">Level @app.ApprovalLevel</span>
                                                            }
                                                            else
                                                            {
                                                                <span class="badge bg-secondary">Not Started</span>
                                                            }
                                                        </td>
                                                        <td>
                                                            <div class="btn-group" role="group">
                                                                <a asp-page="/Admin/Applications/Details" asp-route-id="@app.ApplicationID"
                                                                   class="btn btn-sm btn-outline-primary" title="View Details">
                                                                    <i class="fas fa-eye"></i>
                                                                </a>
                                                                @if (canApprove && Model.ApproverLevel.HasValue && app.ApprovalLevel == Model.ApproverLevel.Value - 1)
                                                                {
                                                                    <button type="button" class="btn btn-sm btn-success" title="Approve"
                                                                            onclick="showApprovalModal(@app.ApplicationID, 'approve')">
                                                                        <i class="fas fa-check"></i>
                                                                    </button>
                                                                    <button type="button" class="btn btn-sm btn-danger" title="Reject"
                                                                            onclick="showApprovalModal(@app.ApplicationID, 'reject')">
                                                                        <i class="fas fa-times"></i>
                                                                    </button>
                                                                }
                                                                @if (isAdmin)
                                                                {
                                                                    <a asp-page="/Admin/Applications/Review" asp-route-id="@app.ApplicationID"
                                                                       class="btn btn-sm btn-warning" title="Review">
                                                                        <i class="fas fa-gavel"></i>
                                                                    </a>
                                                                }
                                                            </div>
                                                        </td>
                                                    </tr>
                                                }
                                            </tbody>
                                        </table>
                                    </div>
                                }
                                else
                                {
                                    <div class="text-center py-4">
                                        <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">No pending applications</p>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>

                    <!-- Approved Applications -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingApproved">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseApproved" aria-expanded="false" aria-controls="collapseApproved">
                                <i class="fas fa-check-circle me-2 text-success"></i>
                                Approved Applications
                                <span class="badge bg-success ms-2">@approvedApps.Count</span>
                            </button>
                        </h2>
                        <div id="collapseApproved" class="accordion-collapse collapse" aria-labelledby="headingApproved" data-bs-parent="#applicationsAccordion">
                            <div class="accordion-body">
                                @if (approvedApps.Any())
                                {
                                    <div class="table-container">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>ID</th>
                                                    <th>Company</th>
                                                    <th>Description</th>
                                                    <th>Amount</th>
                                                    <th>Date</th>
                                                    <th>Status</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach (var app in approvedApps)
                                                {
                                                    <tr>
                                                        <td><strong>#@app.ApplicationID</strong></td>
                                                        <td>
                                                            <div class="fw-bold">@app.Company?.Name</div>
                                                        </td>
                                                        <td>
                                                            <div class="text-truncate" style="max-width: 200px;" title="@app.Description">
                                                                @app.Description
                                                            </div>
                                                        </td>
                                                        <td><strong>$@app.RequestedCash.ToString("N2")</strong></td>
                                                        <td>@app.DateRequested.ToString("MMM dd, yyyy")</td>
                                                        <td>
                                                            @if (app.IsDisbursed)
                                                            {
                                                                <span class="status-indicator status-disbursed">Disbursed</span>
                                                            }
                                                            else
                                                            {
                                                                <span class="status-indicator status-approved">Approved</span>
                                                            }
                                                        </td>
                                                        <td>
                                                            <div class="btn-group" role="group">
                                                                <a asp-page="/Admin/Applications/Details" asp-route-id="@app.ApplicationID"
                                                                   class="btn btn-sm btn-outline-primary" title="View Details">
                                                                    <i class="fas fa-eye"></i>
                                                                </a>
                                                                @if (!app.IsDisbursed)
                                                                {
                                                                    <a asp-page="/Admin/Applications/Disburse" asp-route-id="@app.ApplicationID"
                                                                       class="btn btn-sm btn-success" title="Disburse">
                                                                        <i class="fas fa-money-bill-wave"></i>
                                                                    </a>
                                                                }
                                                            </div>
                                                        </td>
                                                    </tr>
                                                }
                                            </tbody>
                                        </table>
                                    </div>
                                }
                                else
                                {
                                    <div class="text-center py-4">
                                        <i class="fas fa-check-circle fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">No approved applications</p>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>

                    <!-- Rejected Applications -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingRejected">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseRejected" aria-expanded="false" aria-controls="collapseRejected">
                                <i class="fas fa-times-circle me-2 text-danger"></i>
                                Rejected Applications
                                <span class="badge bg-danger ms-2">@rejectedApps.Count</span>
                            </button>
                        </h2>
                        <div id="collapseRejected" class="accordion-collapse collapse" aria-labelledby="headingRejected" data-bs-parent="#applicationsAccordion">
                            <div class="accordion-body">
                                @if (rejectedApps.Any())
                                {
                                    <div class="table-container">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>ID</th>
                                                    <th>Company</th>
                                                    <th>Description</th>
                                                    <th>Amount</th>
                                                    <th>Date</th>
                                                    <th>Comment</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach (var app in rejectedApps)
                                                {
                                                    <tr>
                                                        <td><strong>#@app.ApplicationID</strong></td>
                                                        <td>
                                                            <div class="fw-bold">@app.Company?.Name</div>
                                                        </td>
                                                        <td>
                                                            <div class="text-truncate" style="max-width: 200px;" title="@app.Description">
                                                                @app.Description
                                                            </div>
                                                        </td>
                                                        <td><strong>$@app.RequestedCash.ToString("N2")</strong></td>
                                                        <td>@app.DateRequested.ToString("MMM dd, yyyy")</td>
                                                        <td>
                                                            <div class="text-truncate" style="max-width: 150px;" title="@app.ApprovalComment">
                                                                @(string.IsNullOrEmpty(app.ApprovalComment) ? "No comment" : app.ApprovalComment)
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <a asp-page="/Admin/Applications/Details" asp-route-id="@app.ApplicationID"
                                                               class="btn btn-sm btn-outline-primary" title="View Details">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                }
                                            </tbody>
                                        </table>
                                    </div>
                                }
                                else
                                {
                                    <div class="text-center py-4">
                                        <i class="fas fa-times-circle fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">No rejected applications</p>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @if (!Model.Applications.Any())
        {
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-file-alt fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">No applications found</h5>
                            <p class="text-muted">No applications match your current filters.</p>
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>
</div>
