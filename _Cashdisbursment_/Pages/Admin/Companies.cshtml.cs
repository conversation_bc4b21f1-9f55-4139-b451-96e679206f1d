using _Cashdisbursment_.Models;
using _Cashdisbursment_.Services;
using _Cashdisbursment_.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace _Cashdisbursment_.Pages.Admin
{
    public class CompaniesModel : PageModel
    {
        private readonly CompanyService _companyService;

        public CompaniesModel(CompanyService companyService)
        {
            _companyService = companyService;
        }

        public User? CurrentUser { get; set; }
        public List<Models.Company> Companies { get; set; } = new();

        [BindProperty(SupportsGet = true)]
        public string? SearchTerm { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? StatusFilter { get; set; }

        [TempData]
        public string? SuccessMessage { get; set; }

        [TempData]
        public string? ErrorMessage { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);

            if (CurrentUser == null || !AuthUtility.CanManageCompanies(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            var allCompanies = await _companyService.GetAllCompaniesAsync();
            Companies = FilterCompanies(allCompanies);

            return Page();
        }

        public async Task<IActionResult> OnPostToggleStatusAsync(int companyId)
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);
            
            if (CurrentUser == null || !AuthUtility.IsAdmin(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            try
            {
                var success = await _companyService.ToggleCompanyStatusAsync(companyId);
                
                if (success)
                {
                    SuccessMessage = "Company status updated successfully.";
                }
                else
                {
                    ErrorMessage = "Failed to update company status.";
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = "An error occurred while updating company status.";
                // Log the exception in a real application
            }

            return RedirectToPage();
        }

        private List<Models.Company> FilterCompanies(List<Models.Company> companies)
        {
            var filtered = companies.AsEnumerable();

            // Search filter
            if (!string.IsNullOrEmpty(SearchTerm))
            {
                filtered = filtered.Where(c => 
                    c.Name.Contains(SearchTerm, StringComparison.OrdinalIgnoreCase) ||
                    c.Email.Contains(SearchTerm, StringComparison.OrdinalIgnoreCase));
            }

            // Status filter
            if (!string.IsNullOrEmpty(StatusFilter))
            {
                filtered = StatusFilter.ToLower() switch
                {
                    "active" => filtered.Where(c => c.Status),
                    "inactive" => filtered.Where(c => !c.Status),
                    _ => filtered
                };
            }

            return filtered.OrderBy(c => c.Name).ToList();
        }
    }
}
