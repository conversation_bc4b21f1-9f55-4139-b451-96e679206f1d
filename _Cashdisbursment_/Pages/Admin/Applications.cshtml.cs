using _Cashdisbursment_.Models;
using _Cashdisbursment_.Services;
using _Cashdisbursment_.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace _Cashdisbursment_.Pages.Admin
{
    public class ApplicationsModel : PageModel
    {
        private readonly ApplicationService _applicationService;
        private readonly CompanyService _companyService;
        private readonly ApprovalService _approvalService;

        public ApplicationsModel(ApplicationService applicationService, CompanyService companyService, ApprovalService approvalService)
        {
            _applicationService = applicationService;
            _companyService = companyService;
            _approvalService = approvalService;
        }

        public User? CurrentUser { get; set; }
        public int? ApproverLevel { get; set; }
        public bool IsApprover { get; set; }
        public List<Application> Applications { get; set; } = new();
        public List<Models.Company> Companies { get; set; } = new();

        // Filter properties
        [BindProperty(SupportsGet = true)]
        public string? SearchTerm { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? StatusFilter { get; set; }

        [BindProperty(SupportsGet = true)]
        public int? CompanyFilter { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? DateRange { get; set; }

        // Statistics
        public int TotalApplications { get; set; }
        public int PendingApplications { get; set; }
        public int ApprovedApplications { get; set; }
        public double TotalAmount { get; set; }

        [TempData]
        public string? SuccessMessage { get; set; }

        [TempData]
        public string? ErrorMessage { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);

            if (CurrentUser == null || !AuthUtility.CanViewApplications(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            try
            {
                // Check if user is an approver and get their level
                IsApprover = AuthUtility.IsApprover(HttpContext.Session);
                ApproverLevel = AuthUtility.GetApproverLevel(HttpContext.Session);

                // Load companies for filter dropdown
                Companies = await _companyService.GetAllCompaniesAsync();

                // Load applications based on user role
                List<Application> allApplications;
                if (IsApprover && ApproverLevel.HasValue)
                {
                    // Approvers see applications visible to their level
                    allApplications = await _applicationService.GetApplicationsVisibleToApproverAsync(ApproverLevel.Value);
                }
                else
                {
                    // Admins see all applications
                    allApplications = await _applicationService.GetAllApplicationsAsync();
                }

                // Apply filters
                Applications = FilterApplications(allApplications);

                // Calculate statistics
                CalculateStatistics(allApplications);

                return Page();
            }
            catch (Exception)
            {
                ErrorMessage = "An error occurred while loading applications. Please try again.";
                return Page();
            }
        }

        private List<Application> FilterApplications(List<Application> applications)
        {
            var filtered = applications.AsEnumerable();

            // Search filter
            if (!string.IsNullOrEmpty(SearchTerm))
            {
                filtered = filtered.Where(a => 
                    a.Description.Contains(SearchTerm, StringComparison.OrdinalIgnoreCase) ||
                    a.Purpose.Contains(SearchTerm, StringComparison.OrdinalIgnoreCase) ||
                    (a.Company?.Name.Contains(SearchTerm, StringComparison.OrdinalIgnoreCase) ?? false));
            }

            // Status filter
            if (!string.IsNullOrEmpty(StatusFilter))
            {
                filtered = StatusFilter.ToLower() switch
                {
                    "pending" => filtered.Where(a => a.Status == "Pending" && !a.IsDisbursed),
                    "approved" => filtered.Where(a => a.Status == "Approved" && !a.IsDisbursed),
                    "disbursed" => filtered.Where(a => a.IsDisbursed),
                    "rejected" => filtered.Where(a => a.Status == "Rejected"),
                    _ => filtered
                };
            }

            // Company filter
            if (CompanyFilter.HasValue)
            {
                filtered = filtered.Where(a => a.CompanyID == CompanyFilter.Value);
            }

            // Date range filter
            if (!string.IsNullOrEmpty(DateRange) && int.TryParse(DateRange, out int days))
            {
                var cutoffDate = DateTime.Now.AddDays(-days);
                filtered = filtered.Where(a => a.DateRequested >= cutoffDate);
            }

            return filtered.OrderByDescending(a => a.DateRequested).ToList();
        }

        public async Task<IActionResult> OnPostApproveAsync(int id, string comment = "")
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);

            if (CurrentUser == null || !AuthUtility.CanApprove(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            try
            {
                ApproverLevel = AuthUtility.GetApproverLevel(HttpContext.Session);
                if (!ApproverLevel.HasValue)
                {
                    ErrorMessage = "You are not configured as an approver. Please contact the administrator.";
                    return RedirectToPage();
                }

                // Get the application
                var application = await _applicationService.GetApplicationByIdAsync(id);
                if (application == null)
                {
                    ErrorMessage = "Application not found.";
                    return RedirectToPage();
                }

                // Check if this approver can approve this application
                if (application.ApprovalLevel != ApproverLevel.Value - 1 || application.Status != "Pending")
                {
                    ErrorMessage = "You cannot approve this application at this time.";
                    return RedirectToPage();
                }

                // Check if there are more approvers after this level
                var maxApprovalLevel = await _approvalService.GetMaxApprovalLevelAsync();
                var newApprovalLevel = ApproverLevel.Value;
                var newStatus = ApproverLevel.Value >= maxApprovalLevel ? "Approved" : "Pending";

                // Update the application
                var success = await _applicationService.UpdateApplicationApprovalAsync(id, newApprovalLevel, newStatus, comment);

                if (success)
                {
                    SuccessMessage = $"Application #{id} has been approved successfully.";
                }
                else
                {
                    ErrorMessage = "Failed to approve application. Please try again.";
                }
            }
            catch (Exception)
            {
                ErrorMessage = "An error occurred while approving the application. Please try again.";
            }

            return RedirectToPage();
        }

        public async Task<IActionResult> OnPostRejectAsync(int id, string comment = "")
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);

            if (CurrentUser == null || !AuthUtility.CanApprove(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            try
            {
                ApproverLevel = AuthUtility.GetApproverLevel(HttpContext.Session);
                if (!ApproverLevel.HasValue)
                {
                    ErrorMessage = "You are not configured as an approver. Please contact the administrator.";
                    return RedirectToPage();
                }

                // Get the application
                var application = await _applicationService.GetApplicationByIdAsync(id);
                if (application == null)
                {
                    ErrorMessage = "Application not found.";
                    return RedirectToPage();
                }

                // Check if this approver can reject this application
                if (application.ApprovalLevel != ApproverLevel.Value - 1 || application.Status != "Pending")
                {
                    ErrorMessage = "You cannot reject this application at this time.";
                    return RedirectToPage();
                }

                // Update the application
                var success = await _applicationService.UpdateApplicationApprovalAsync(id, ApproverLevel.Value, "Rejected", comment);

                if (success)
                {
                    SuccessMessage = $"Application #{id} has been rejected.";
                }
                else
                {
                    ErrorMessage = "Failed to reject application. Please try again.";
                }
            }
            catch (Exception)
            {
                ErrorMessage = "An error occurred while rejecting the application. Please try again.";
            }

            return RedirectToPage();
        }

        private void CalculateStatistics(List<Application> allApplications)
        {
            TotalApplications = allApplications.Count;
            PendingApplications = allApplications.Count(a => a.Status == "Pending" && !a.IsDisbursed);
            ApprovedApplications = allApplications.Count(a => a.Status == "Approved");
            TotalAmount = allApplications.Sum(a => a.RequestedCash);
        }
    }
}
