@page
@model _Cashdisbursment_.Pages.Admin.AcquittalsModel
@{
    ViewData["Title"] = "Acquittals Management";
    
}

<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-receipt me-2"></i>Acquittals Management
                </h1>
            </div>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(Model.SuccessMessage))
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>@Model.SuccessMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>@Model.ErrorMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-primary">Total Acquittals</h5>
                    <h3 class="mb-0">@Model.Acquittals.Count</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-success">Total Disbursed</h5>
                    <h3 class="mb-0">$@Model.TotalDisbursed.ToString("N2")</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-info">Total Acquitted</h5>
                    <h3 class="mb-0">$@Model.TotalAcquitted.ToString("N2")</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-warning">Outstanding</h5>
                    <h3 class="mb-0">$@Model.Outstanding.ToString()</h3>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">Search</label>
                            <input type="text" name="SearchTerm" value="@Model.SearchTerm" class="form-control" placeholder="Company name or description..." />
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Company</label>
                            <select name="CompanyFilter" class="form-select">
                                <option value="">All Companies</option>
                                @foreach (var company in Model.Companies)
                                {
                                    <option value="@company.CompanyID" selected="@(Model.CompanyFilter == company.CompanyID)">@company.Name</option>
                                }
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Status</label>
                            <select name="StatusFilter" class="form-select">
                                <option value="">All Status</option>
                                <option value="Complete" selected="@(Model.StatusFilter == "Complete")">Complete</option>
                                <option value="Partial" selected="@(Model.StatusFilter == "Partial")">Partial</option>
                                <option value="Outstanding" selected="@(Model.StatusFilter == "Outstanding")">Outstanding</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>Filter
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Acquittals Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        Acquittals 
                        <span class="badge bg-secondary">@Model.Acquittals.Count</span>
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.Acquittals.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Application ID</th>
                                        <th>Company</th>
                                        <th>Description</th>
                                        <th>Disbursed</th>
                                        <th>Acquitted</th>
                                        <th>Outstanding</th>
                                        <th>Status</th>
                                        <th>Date Disbursed</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var acquittal in Model.Acquittals)
                                    {
                                        var acquittedAmount = Model.AcquittalSubmissions
                                            .Where(s => s.AcquittalID == acquittal.AcquittalID)
                                            .Sum(s => s.Amount);
                                        var disbursedAmount = acquittal.Application?.DisbursedCash ?? 0;
                                        var outstanding = disbursedAmount - acquittedAmount;
                                        var status = outstanding <= 0 ? "Complete" : (acquittedAmount > 0 ? "Partial" : "Outstanding");
                                        
                                        <tr>
                                            <td><strong>#@acquittal.Application?.ApplicationID</strong></td>
                                            <td>
                                                <div class="fw-bold">@acquittal.Application?.Company?.Name</div>
                                            </td>
                                            <td>
                                                <div class="text-truncate" style="max-width: 200px;" title="@acquittal.Application?.Description">
                                                    @acquittal.Application?.Description
                                                </div>
                                            </td>
                                            <td>
                                                <span class="fw-bold text-success">$@disbursedAmount.ToString("N2")</span>
                                            </td>
                                            <td>
                                                <span class="fw-bold text-info">$@acquittedAmount.ToString("N2")</span>
                                            </td>
                                            <td>
                                                @if (outstanding > 0)
                                                {
                                                    <span class="fw-bold text-warning">$@outstanding.ToString("N2")</span>
                                                }
                                                else
                                                {
                                                    <span class="fw-bold text-success">$0.00</span>
                                                }
                                            </td>
                                            <td>
                                                @if (status == "Complete")
                                                {
                                                    <span class="badge bg-success">Complete</span>
                                                }
                                                else if (status == "Partial")
                                                {
                                                    <span class="badge bg-warning">Partial</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">Outstanding</span>
                                                }
                                            </td>
                                            <td>@acquittal.Application?.DateDisbursed?.ToString("MMM dd, yyyy")</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-page="/Admin/Acquittals/Details" asp-route-id="@acquittal.AcquittalID" 
                                                       class="btn btn-sm btn-outline-primary" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Acquittals Found</h5>
                            <p class="text-muted">No disbursed applications require acquittal yet.</p>
                            <a asp-page="/Admin/Applications" class="btn btn-primary">
                                <i class="fas fa-file-alt me-2"></i>View Applications
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .status-complete {
        color: #28a745;
        font-weight: 500;
    }
    
    .status-partial {
        color: #ffc107;
        font-weight: 500;
    }
    
    .status-outstanding {
        color: #dc3545;
        font-weight: 500;
    }
</style>
