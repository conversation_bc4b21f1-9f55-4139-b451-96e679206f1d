using _Cashdisbursment_.Models;
using _Cashdisbursment_.Services;
using _Cashdisbursment_.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace _Cashdisbursment_.Pages.Admin
{
    public class AcquittalsModel : PageModel
    {
        private readonly AcquittalService _acquittalService;
        private readonly CompanyService _companyService;

        public AcquittalsModel(AcquittalService acquittalService, CompanyService companyService)
        {
            _acquittalService = acquittalService;
            _companyService = companyService;
        }

        public User? CurrentUser { get; set; }
        public List<Acquittal> Acquittals { get; set; } = new();
        public List<AcquittalSubmission> AcquittalSubmissions { get; set; } = new();
        public List<Models.Company> Companies { get; set; } = new();
        public double TotalDisbursed { get; set; }
        public double TotalAcquitted { get; set; }
        public double Outstanding { get; set; }
        // Filter properties
        [BindProperty(SupportsGet = true)]
        public string? SearchTerm { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? StatusFilter { get; set; }

        [BindProperty(SupportsGet = true)]
        public int? CompanyFilter { get; set; }

        [TempData]
        public string? SuccessMessage { get; set; }

        [TempData]
        public string? ErrorMessage { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);

            if (CurrentUser == null || !AuthUtility.CanViewAcquittals(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            try
            {
                // Load companies for filter dropdown
                Companies = await _companyService.GetAllCompaniesAsync();

                // Get all acquittals
                var allAcquittals = await _acquittalService.GetAllAcquittalsAsync();

                // Get all acquittal submissions
                foreach (var acquittal in allAcquittals)
                {
                    var submissions = await _acquittalService.GetSubmissionsByAcquittalIdAsync(acquittal.AcquittalID);
                    AcquittalSubmissions.AddRange(submissions);
                }

                // Apply filters
                Acquittals = FilterAcquittals(allAcquittals);

                // Calculate totals
                TotalDisbursed = allAcquittals.Sum(a => a.Application?.DisbursedCash ?? 0);
                TotalAcquitted = AcquittalSubmissions.Sum(s => s.Amount);
                Outstanding = TotalDisbursed - TotalAcquitted;

                return Page();
            }
            catch (Exception ex)
            {
                ErrorMessage = "An error occurred while loading acquittals. Please try again.";
                return Page();
            }
        }

        private List<Acquittal> FilterAcquittals(List<Acquittal> acquittals)
        {
            var filtered = acquittals.AsEnumerable();

            // Search filter
            if (!string.IsNullOrEmpty(SearchTerm))
            {
                var searchLower = SearchTerm.ToLower();
                filtered = filtered.Where(a =>
                {
                    var companyName = a.Application?.Company?.Name?.ToLower();
                    var description = a.Application?.Description?.ToLower();
                    return (companyName != null && companyName.Contains(searchLower)) ||
                           (description != null && description.Contains(searchLower));
                });
            }

            // Company filter
            if (CompanyFilter.HasValue)
            {
                filtered = filtered.Where(a => a.Application != null && a.Application.CompanyID == CompanyFilter.Value);
            }

            // Status filter
            if (!string.IsNullOrEmpty(StatusFilter))
            {
                filtered = filtered.Where(a =>
                {
                    var acquittedAmount = AcquittalSubmissions
                        .Where(s => s.AcquittalID == a.AcquittalID)
                        .Sum(s => s.Amount);
                    var disbursedAmount = a.Application?.DisbursedCash ?? 0;
                    var outstanding = disbursedAmount - acquittedAmount;

                    if (StatusFilter == "Complete")
                        return outstanding <= 0;
                    else if (StatusFilter == "Partial")
                        return outstanding > 0 && acquittedAmount > 0;
                    else if (StatusFilter == "Outstanding")
                        return outstanding > 0 && acquittedAmount == 0;
                    else
                        return true;
                });
            }

            return filtered.OrderByDescending(a => a.Application?.DateDisbursed ?? DateTime.MinValue).ToList();
        }
    }
}
