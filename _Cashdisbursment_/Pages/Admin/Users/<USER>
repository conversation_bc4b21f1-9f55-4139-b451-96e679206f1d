using _Cashdisbursment_.Models;
using _Cashdisbursment_.Services;
using _Cashdisbursment_.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.ComponentModel.DataAnnotations;

namespace _Cashdisbursment_.Pages.Admin.Users
{
    public class CreateModel : PageModel
    {
        private readonly UserService _userService;
        private readonly EmailService _emailService;

        public CreateModel(UserService userService, EmailService emailService)
        {
            _userService = userService;
            _emailService = emailService;
        }

        public User? CurrentUser { get; set; }

        [BindProperty]
        [Required]
        [EmailAddress]
        [Display(Name = "Email Address")]
        public string Email { get; set; } = string.Empty;

        [BindProperty]
        [Required]
        [Display(Name = "Role")]
        public string Role { get; set; } = "Admin";

        [BindProperty]
        [Display(Name = "Send Credentials via Email")]
        public bool SendCredentials { get; set; } = true;

        [TempData]
        public string? SuccessMessage { get; set; }

        [TempData]
        public string? ErrorMessage { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);

            if (CurrentUser == null || !AuthUtility.CanManageUsers(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);

            if (CurrentUser == null || !AuthUtility.CanManageUsers(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            if (!ModelState.IsValid)
            {
                return Page();
            }

            try
            {
                // Check if user email already exists
                var existingUser = await _userService.GetUserByEmailAsync(Email);
                if (existingUser != null)
                {
                    ErrorMessage = "A user with this email already exists.";
                    return Page();
                }

                // Generate random password
                var password = _userService.GenerateRandomPassword(10);

                // Create user
                var user = new User
                {
                    Email = Email.Trim().ToLower(),
                    Password = password,
                    Status = true,
                    CompanyID = null, // Admin and Approver users don't belong to companies
                    Role = Role
                };

                var userCreated = await _userService.CreateUserAsync(user);

                if (userCreated)
                {
                    // Send credentials email if requested
                    if (SendCredentials)
                    {
                        await _emailService.SendUserCredentialsEmailAsync(Email, password, Role);
                    }

                    SuccessMessage = $"{Role} user '{Email}' created successfully!" +
                        (SendCredentials ? " Login credentials have been sent via email." : "");
                    
                    return RedirectToPage("/Admin/Users");
                }
                else
                {
                    ErrorMessage = "Failed to create user. Please try again.";
                    return Page();
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = "An error occurred while creating the user. Please try again.";
                return Page();
            }
        }
    }
}
