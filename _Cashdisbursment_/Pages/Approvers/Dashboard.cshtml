@page
@model _Cashdisbursment_.Pages.Approvers.DashboardModel
@{
    ViewData["Title"] = "Approver Dashboard";
}

<div class="container">
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="h3 mb-1">Approver Dashboard</h1>
                <p class="text-muted mb-0">Level @Model.ApproverLevel Approver - @Model.CurrentUser?.Email</p>
            </div>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(Model.SuccessMessage))
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>@Model.SuccessMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>@Model.ErrorMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <div class="py-section">
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="metric-icon bg-warning">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="ms-3">
                                <div class="metric-value">@Model.PendingForApproval</div>
                                <div class="metric-label">Pending Approval</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="metric-icon bg-success">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="ms-3">
                                <div class="metric-value">@Model.TotalApproved</div>
                                <div class="metric-label">Approved</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="metric-icon bg-danger">
                                <i class="fas fa-times-circle"></i>
                            </div>
                            <div class="ms-3">
                                <div class="metric-value">@Model.TotalRejected</div>
                                <div class="metric-label">Rejected</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="metric-icon bg-primary">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="ms-3">
                                <div class="metric-value">$@Model.TotalAmountApproved.ToString("N0")</div>
                                <div class="metric-label">Total Approved</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title mb-3">Quick Actions</h5>
                        <div class="row">
                            <div class="col-md-4">
                                <a asp-page="/Approvers/Applications" class="btn btn-outline-primary w-100 mb-2">
                                    <i class="fas fa-file-alt me-2"></i>Review Applications
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a asp-page="/Approvers/Acquittals" class="btn btn-outline-info w-100 mb-2">
                                    <i class="fas fa-receipt me-2"></i>View Acquittals
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a asp-page="/Approvers/Applications" asp-route-StatusFilter="pending" class="btn btn-warning w-100 mb-2">
                                    <i class="fas fa-clock me-2"></i>Pending Items (@Model.PendingForApproval)
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Applications -->
        @if (Model.PendingApplications.Any())
        {
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Applications Pending Your Approval</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-container">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Company</th>
                                            <th>Description</th>
                                            <th>Amount</th>
                                            <th>Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var app in Model.PendingApplications.Take(5))
                                        {
                                            <tr>
                                                <td><strong>#@app.ApplicationID</strong></td>
                                                <td>@app.Company?.Name</td>
                                                <td>
                                                    <div class="text-truncate" style="max-width: 200px;" title="@app.Description">
                                                        @app.Description
                                                    </div>
                                                </td>
                                                <td><strong>$@app.RequestedCash.ToString("N2")</strong></td>
                                                <td>@app.DateRequested.ToString("MMM dd, yyyy")</td>
                                                <td>
                                                    <a asp-page="/Approvers/Applications/Review" asp-route-id="@app.ApplicationID"
                                                       class="btn btn-sm btn-warning" title="Review">
                                                        <i class="fas fa-gavel"></i> Review
                                                    </a>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                            @if (Model.PendingApplications.Count > 5)
                            {
                                <div class="text-center mt-3">
                                    <a asp-page="/Approvers/Applications" asp-route-StatusFilter="pending" class="btn btn-outline-primary">
                                        View All @Model.PendingApplications.Count Pending Applications
                                    </a>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }

        <!-- Recent Applications -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Recent Applications</h5>
                    </div>
                    <div class="card-body">
                        @if (Model.RecentApplications.Any())
                        {
                            <div class="table-container">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Company</th>
                                            <th>Description</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                            <th>Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var app in Model.RecentApplications)
                                        {
                                            <tr>
                                                <td><strong>#@app.ApplicationID</strong></td>
                                                <td>@app.Company?.Name</td>
                                                <td>
                                                    <div class="text-truncate" style="max-width: 200px;" title="@app.Description">
                                                        @app.Description
                                                    </div>
                                                </td>
                                                <td><strong>$@app.RequestedCash.ToString("N2")</strong></td>
                                                <td>
                                                    @if (app.Status == "Pending")
                                                    {
                                                        <span class="status-indicator status-pending">Pending</span>
                                                    }
                                                    else if (app.Status == "Approved")
                                                    {
                                                        <span class="status-indicator status-approved">Approved</span>
                                                    }
                                                    else if (app.Status == "Rejected")
                                                    {
                                                        <span class="status-indicator status-rejected">Rejected</span>
                                                    }
                                                    else if (app.IsDisbursed)
                                                    {
                                                        <span class="status-indicator status-disbursed">Disbursed</span>
                                                    }
                                                </td>
                                                <td>@app.DateRequested.ToString("MMM dd, yyyy")</td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-4">
                                <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No recent applications</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
