using _Cashdisbursment_.Models;
using _Cashdisbursment_.Services;
using _Cashdisbursment_.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace _Cashdisbursment_.Pages.Approvers
{
    public class DashboardModel : PageModel
    {
        private readonly ApplicationService _applicationService;
        private readonly ApprovalService _approvalService;
        private readonly AcquittalService _acquittalService;

        public DashboardModel(ApplicationService applicationService, ApprovalService approvalService, AcquittalService acquittalService)
        {
            _applicationService = applicationService;
            _approvalService = approvalService;
            _acquittalService = acquittalService;
        }

        public User? CurrentUser { get; set; }
        public int ApproverLevel { get; set; }
        public int PendingForApproval { get; set; }
        public int TotalApplicationsReviewed { get; set; }
        public int TotalApproved { get; set; }
        public int TotalRejected { get; set; }
        public double TotalAmountApproved { get; set; }
        public List<Application> RecentApplications { get; set; } = new();
        public List<Application> PendingApplications { get; set; } = new();

        [TempData]
        public string? SuccessMessage { get; set; }

        [TempData]
        public string? ErrorMessage { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            // Redirect approvers to admin dashboard
            return RedirectToPage("/Admin/Dashboard");
        }


    }
}
