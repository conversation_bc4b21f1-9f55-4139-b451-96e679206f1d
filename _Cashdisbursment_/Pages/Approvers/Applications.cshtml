@page
@model _Cashdisbursment_.Pages.Approvers.ApplicationsModel
@{
    ViewData["Title"] = "Review Applications";
}

<div class="container">
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="h3 mb-1">Review Applications</h1>
                <p class="text-muted mb-0">Level @Model.ApproverLevel Approver - Review and approve applications</p>
            </div>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(Model.SuccessMessage))
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>@Model.SuccessMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>@Model.ErrorMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <div class="py-section">
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="metric-icon bg-warning">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="ms-3">
                                <div class="metric-value">@Model.PendingForApproval</div>
                                <div class="metric-label">Pending My Approval</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="metric-icon bg-success">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="ms-3">
                                <div class="metric-value">@Model.ApprovedByMe</div>
                                <div class="metric-label">Approved by Me</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="metric-icon bg-danger">
                                <i class="fas fa-times-circle"></i>
                            </div>
                            <div class="ms-3">
                                <div class="metric-value">@Model.RejectedByMe</div>
                                <div class="metric-label">Rejected by Me</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="metric-icon bg-info">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div class="ms-3">
                                <div class="metric-value">@Model.TotalApplications</div>
                                <div class="metric-label">Total Visible</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter and Search -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form method="get" class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">Search</label>
                                <input type="text" name="SearchTerm" value="@Model.SearchTerm" class="form-control"
                                       placeholder="Search by company or description...">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Status</label>
                                <select name="StatusFilter" class="form-select">
                                    <option value="">All Status</option>
                                    <option value="pending-for-me" selected="@(Model.StatusFilter == "pending-for-me")">Pending My Approval</option>
                                    <option value="pending" selected="@(Model.StatusFilter == "pending")">All Pending</option>
                                    <option value="approved" selected="@(Model.StatusFilter == "approved")">Approved</option>
                                    <option value="disbursed" selected="@(Model.StatusFilter == "disbursed")">Disbursed</option>
                                    <option value="rejected" selected="@(Model.StatusFilter == "rejected")">Rejected</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Company</label>
                                <select name="CompanyFilter" class="form-select">
                                    <option value="">All Companies</option>
                                    @foreach (var company in Model.Companies)
                                    {
                                        <option value="@company.CompanyID" selected="@(Model.CompanyFilter == company.CompanyID)">
                                            @company.Name
                                        </option>
                                    }
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Date Range</label>
                                <select name="DateRange" class="form-select">
                                    <option value="">All Time</option>
                                    <option value="7" selected="@(Model.DateRange == "7")">Last 7 days</option>
                                    <option value="30" selected="@(Model.DateRange == "30")">Last 30 days</option>
                                    <option value="90" selected="@(Model.DateRange == "90")">Last 90 days</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Approval Level</label>
                                <select name="ApprovalFilter" class="form-select">
                                    <option value="">All Levels</option>
                                    <option value="my-level" selected="@(Model.ApprovalFilter == "my-level")">My Level & Above</option>
                                    <option value="below-my-level" selected="@(Model.ApprovalFilter == "below-my-level")">Below My Level</option>
                                    <option value="above-my-level" selected="@(Model.ApprovalFilter == "above-my-level")">Above My Level</option>
                                </select>
                            </div>
                            <div class="col-md-1">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-outline-primary">
                                        <i class="fas fa-search me-1"></i>Filter
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Applications Accordion -->
        <div class="row">
            <div class="col-12">
                @{
                    var pendingForMe = Model.Applications.Where(a => a.Status == "Pending" && a.ApprovalLevel == Model.ApproverLevel - 1).ToList();
                    var pendingOthers = Model.Applications.Where(a => a.Status == "Pending" && a.ApprovalLevel != Model.ApproverLevel - 1).ToList();
                    var approvedApps = Model.Applications.Where(a => a.Status == "Approved").ToList();
                    var rejectedApps = Model.Applications.Where(a => a.Status == "Rejected").ToList();
                }

                <div class="accordion applications-accordion" id="applicationsAccordion">
                    <!-- Pending for My Approval -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingPendingMe">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapsePendingMe" aria-expanded="true" aria-controls="collapsePendingMe">
                                <i class="fas fa-gavel me-2 text-warning"></i>
                                Pending My Approval
                                <span class="badge bg-warning ms-2">@pendingForMe.Count</span>
                            </button>
                        </h2>
                        <div id="collapsePendingMe" class="accordion-collapse collapse show" aria-labelledby="headingPendingMe" data-bs-parent="#applicationsAccordion">
                            <div class="accordion-body">
                                @if (pendingForMe.Any())
                                {
                                    <div class="table-container">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>ID</th>
                                                    <th>Company</th>
                                                    <th>Description</th>
                                                    <th>Amount</th>
                                                    <th>Date</th>
                                                    <th>Level</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach (var app in pendingForMe)
                                                {
                                                    <tr>
                                                        <td><strong>#@app.ApplicationID</strong></td>
                                                        <td>
                                                            <div class="fw-bold">@app.Company?.Name</div>
                                                        </td>
                                                        <td>
                                                            <div class="text-truncate" style="max-width: 200px;" title="@app.Description">
                                                                @app.Description
                                                            </div>
                                                        </td>
                                                        <td><strong>$@app.RequestedCash.ToString("N2")</strong></td>
                                                        <td>@app.DateRequested.ToString("MMM dd, yyyy")</td>
                                                        <td>
                                                            <span class="badge bg-info">Level @app.ApprovalLevel</span>
                                                        </td>
                                                        <td>
                                                            <div class="btn-group" role="group">
                                                                <button type="button" class="btn btn-sm btn-success" 
                                                                        onclick="showApprovalModal(@app.ApplicationID, 'approve', '@app.Company?.Name', '@app.Description')">
                                                                    <i class="fas fa-check"></i>
                                                                </button>
                                                                <button type="button" class="btn btn-sm btn-danger"
                                                                        onclick="showApprovalModal(@app.ApplicationID, 'reject', '@app.Company?.Name', '@app.Description')">
                                                                    <i class="fas fa-times"></i>
                                                                </button>
                                                                <a asp-page="/Approvers/Applications/Details" asp-route-id="@app.ApplicationID"
                                                                   class="btn btn-sm btn-outline-primary" title="View Details">
                                                                    <i class="fas fa-eye"></i>
                                                                </a>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                }
                                            </tbody>
                                        </table>
                                    </div>
                                }
                                else
                                {
                                    <div class="text-center py-4">
                                        <i class="fas fa-gavel fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">No applications pending your approval</p>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>

                    <!-- Other Pending Applications -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingPendingOthers">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapsePendingOthers" aria-expanded="false" aria-controls="collapsePendingOthers">
                                <i class="fas fa-clock me-2 text-info"></i>
                                Other Pending Applications
                                <span class="badge bg-info ms-2">@pendingOthers.Count</span>
                            </button>
                        </h2>
                        <div id="collapsePendingOthers" class="accordion-collapse collapse" aria-labelledby="headingPendingOthers" data-bs-parent="#applicationsAccordion">
                            <div class="accordion-body">
                                @if (pendingOthers.Any())
                                {
                                    <div class="table-container">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>ID</th>
                                                    <th>Company</th>
                                                    <th>Description</th>
                                                    <th>Amount</th>
                                                    <th>Date</th>
                                                    <th>Level</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach (var app in pendingOthers)
                                                {
                                                    <tr>
                                                        <td><strong>#@app.ApplicationID</strong></td>
                                                        <td>
                                                            <div class="fw-bold">@app.Company?.Name</div>
                                                        </td>
                                                        <td>
                                                            <div class="text-truncate" style="max-width: 200px;" title="@app.Description">
                                                                @app.Description
                                                            </div>
                                                        </td>
                                                        <td><strong>$@app.RequestedCash.ToString("N2")</strong></td>
                                                        <td>@app.DateRequested.ToString("MMM dd, yyyy")</td>
                                                        <td>
                                                            @if (app.ApprovalLevel < Model.ApproverLevel - 1)
                                                            {
                                                                <span class="badge bg-secondary">Level @app.ApprovalLevel (Below)</span>
                                                            }
                                                            else
                                                            {
                                                                <span class="badge bg-primary">Level @app.ApprovalLevel (Above)</span>
                                                            }
                                                        </td>
                                                        <td>
                                                            <a asp-page="/Approvers/Applications/Details" asp-route-id="@app.ApplicationID"
                                                               class="btn btn-sm btn-outline-primary" title="View Details">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                }
                                            </tbody>
                                        </table>
                                    </div>
                                }
                                else
                                {
                                    <div class="text-center py-4">
                                        <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">No other pending applications</p>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>

                    <!-- Approved Applications -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingApproved">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseApproved" aria-expanded="false" aria-controls="collapseApproved">
                                <i class="fas fa-check-circle me-2 text-success"></i>
                                Approved Applications
                                <span class="badge bg-success ms-2">@approvedApps.Count</span>
                            </button>
                        </h2>
                        <div id="collapseApproved" class="accordion-collapse collapse" aria-labelledby="headingApproved" data-bs-parent="#applicationsAccordion">
                            <div class="accordion-body">
                                @if (approvedApps.Any())
                                {
                                    <div class="table-container">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>ID</th>
                                                    <th>Company</th>
                                                    <th>Description</th>
                                                    <th>Amount</th>
                                                    <th>Date</th>
                                                    <th>Status</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach (var app in approvedApps)
                                                {
                                                    <tr>
                                                        <td><strong>#@app.ApplicationID</strong></td>
                                                        <td>
                                                            <div class="fw-bold">@app.Company?.Name</div>
                                                        </td>
                                                        <td>
                                                            <div class="text-truncate" style="max-width: 200px;" title="@app.Description">
                                                                @app.Description
                                                            </div>
                                                        </td>
                                                        <td><strong>$@app.RequestedCash.ToString("N2")</strong></td>
                                                        <td>@app.DateRequested.ToString("MMM dd, yyyy")</td>
                                                        <td>
                                                            @if (app.IsDisbursed)
                                                            {
                                                                <span class="status-indicator status-disbursed">Disbursed</span>
                                                            }
                                                            else
                                                            {
                                                                <span class="status-indicator status-approved">Approved</span>
                                                            }
                                                        </td>
                                                        <td>
                                                            <a asp-page="/Approvers/Applications/Details" asp-route-id="@app.ApplicationID"
                                                               class="btn btn-sm btn-outline-primary" title="View Details">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                }
                                            </tbody>
                                        </table>
                                    </div>
                                }
                                else
                                {
                                    <div class="text-center py-4">
                                        <i class="fas fa-check-circle fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">No approved applications</p>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>

                    <!-- Rejected Applications -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingRejected">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseRejected" aria-expanded="false" aria-controls="collapseRejected">
                                <i class="fas fa-times-circle me-2 text-danger"></i>
                                Rejected Applications
                                <span class="badge bg-danger ms-2">@rejectedApps.Count</span>
                            </button>
                        </h2>
                        <div id="collapseRejected" class="accordion-collapse collapse" aria-labelledby="headingRejected" data-bs-parent="#applicationsAccordion">
                            <div class="accordion-body">
                                @if (rejectedApps.Any())
                                {
                                    <div class="table-container">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>ID</th>
                                                    <th>Company</th>
                                                    <th>Description</th>
                                                    <th>Amount</th>
                                                    <th>Date</th>
                                                    <th>Comment</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach (var app in rejectedApps)
                                                {
                                                    <tr>
                                                        <td><strong>#@app.ApplicationID</strong></td>
                                                        <td>
                                                            <div class="fw-bold">@app.Company?.Name</div>
                                                        </td>
                                                        <td>
                                                            <div class="text-truncate" style="max-width: 200px;" title="@app.Description">
                                                                @app.Description
                                                            </div>
                                                        </td>
                                                        <td><strong>$@app.RequestedCash.ToString("N2")</strong></td>
                                                        <td>@app.DateRequested.ToString("MMM dd, yyyy")</td>
                                                        <td>
                                                            <div class="text-truncate" style="max-width: 150px;" title="@app.ApprovalComment">
                                                                @(string.IsNullOrEmpty(app.ApprovalComment) ? "No comment" : app.ApprovalComment)
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <a asp-page="/Approvers/Applications/Details" asp-route-id="@app.ApplicationID"
                                                               class="btn btn-sm btn-outline-primary" title="View Details">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                }
                                            </tbody>
                                        </table>
                                    </div>
                                }
                                else
                                {
                                    <div class="text-center py-4">
                                        <i class="fas fa-times-circle fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">No rejected applications</p>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @if (!Model.Applications.Any())
        {
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-file-alt fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">No applications found</h5>
                            <p class="text-muted">No applications match your current filters.</p>
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>
</div>

<!-- Approval Modal -->
<div class="modal fade" id="approvalModal" tabindex="-1" aria-labelledby="approvalModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="approvalModalLabel">Confirm Action</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="approvalForm" method="post">
                <div class="modal-body">
                    <input type="hidden" id="applicationId" name="id" />
                    <div id="approvalContent">
                        <!-- Content will be populated by JavaScript -->
                    </div>
                    <div class="mb-3">
                        <label for="comment" class="form-label">Comment (Optional)</label>
                        <textarea class="form-control" id="comment" name="comment" rows="3" placeholder="Add a comment about your decision..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn" id="confirmButton">Confirm</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function showApprovalModal(applicationId, action, companyName, description) {
    document.getElementById('applicationId').value = applicationId;

    const form = document.getElementById('approvalForm');
    const content = document.getElementById('approvalContent');
    const confirmButton = document.getElementById('confirmButton');

    if (action === 'approve') {
        form.action = '?handler=Approve';
        content.innerHTML = `
            <div class="alert alert-success">
                <h6><i class="fas fa-check-circle me-2"></i>Approve Application #${applicationId}</h6>
                <p class="mb-0"><strong>Company:</strong> ${companyName}</p>
                <p class="mb-0"><strong>Description:</strong> ${description}</p>
            </div>
        `;
        confirmButton.className = 'btn btn-success';
        confirmButton.innerHTML = '<i class="fas fa-check me-1"></i>Approve';
    } else {
        form.action = '?handler=Reject';
        content.innerHTML = `
            <div class="alert alert-danger">
                <h6><i class="fas fa-times-circle me-2"></i>Reject Application #${applicationId}</h6>
                <p class="mb-0"><strong>Company:</strong> ${companyName}</p>
                <p class="mb-0"><strong>Description:</strong> ${description}</p>
            </div>
        `;
        confirmButton.className = 'btn btn-danger';
        confirmButton.innerHTML = '<i class="fas fa-times me-1"></i>Reject';
    }

    // Clear previous comment
    document.getElementById('comment').value = '';

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('approvalModal'));
    modal.show();
}
</script>
