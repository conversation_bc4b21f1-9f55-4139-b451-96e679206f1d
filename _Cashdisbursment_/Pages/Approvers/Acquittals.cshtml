@page
@model _Cashdisbursment_.Pages.Approvers.AcquittalsModel
@{
    ViewData["Title"] = "View Acquittals";
}

<div class="container">
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="h3 mb-1">View Acquittals</h1>
                <p class="text-muted mb-0">Level @Model.ApproverLevel Approver - Monitor fund acquittals</p>
            </div>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(Model.SuccessMessage))
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>@Model.SuccessMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>@Model.ErrorMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <div class="py-section">
        <!-- Summary Cards -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card metric-card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="metric-icon bg-primary">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <div class="ms-3">
                                <div class="metric-value">$@Model.TotalDisbursed.ToString("N2")</div>
                                <div class="metric-label">Total Disbursed</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card metric-card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="metric-icon bg-success">
                                <i class="fas fa-receipt"></i>
                            </div>
                            <div class="ms-3">
                                <div class="metric-value">$@Model.TotalAcquitted.ToString("N2")</div>
                                <div class="metric-label">Total Acquitted</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card metric-card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="metric-icon bg-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="ms-3">
                                <div class="metric-value">$@Model.Outstanding.ToString("N2")</div>
                                <div class="metric-label">Outstanding</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter and Search -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form method="get" class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label">Search</label>
                                <input type="text" name="SearchTerm" value="@Model.SearchTerm" class="form-control"
                                       placeholder="Search by company or description...">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Status</label>
                                <select name="StatusFilter" class="form-select">
                                    <option value="">All Status</option>
                                    <option value="Outstanding" selected="@(Model.StatusFilter == "Outstanding")">Outstanding</option>
                                    <option value="Partial" selected="@(Model.StatusFilter == "Partial")">Partial</option>
                                    <option value="Complete" selected="@(Model.StatusFilter == "Complete")">Complete</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Company</label>
                                <select name="CompanyFilter" class="form-select">
                                    <option value="">All Companies</option>
                                    @foreach (var company in Model.Companies)
                                    {
                                        <option value="@company.CompanyID" selected="@(Model.CompanyFilter == company.CompanyID)">
                                            @company.Name
                                        </option>
                                    }
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-outline-primary">
                                        <i class="fas fa-search me-1"></i>Filter
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Acquittals Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Acquittals Overview</h5>
                    </div>
                    <div class="card-body">
                        @if (Model.Acquittals.Any())
                        {
                            <div class="table-container">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Application ID</th>
                                            <th>Company</th>
                                            <th>Description</th>
                                            <th>Disbursed Amount</th>
                                            <th>Acquitted Amount</th>
                                            <th>Outstanding</th>
                                            <th>Status</th>
                                            <th>Date Disbursed</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var acquittal in Model.Acquittals)
                                        {
                                            var acquittedAmount = Model.AcquittalSubmissions
                                                .Where(s => s.AcquittalID == acquittal.AcquittalID)
                                                .Sum(s => s.Amount);
                                            var disbursedAmount = acquittal.Application?.DisbursedCash ?? 0;
                                            var outstanding = disbursedAmount - acquittedAmount;
                                            var statusClass = outstanding <= 0 ? "success" : (acquittedAmount > 0 ? "warning" : "danger");
                                            var statusText = outstanding <= 0 ? "Complete" : (acquittedAmount > 0 ? "Partial" : "Outstanding");
                                            <tr>
                                                <td><strong>#@acquittal.Application?.ApplicationID</strong></td>
                                                <td>@acquittal.Application?.Company?.Name</td>
                                                <td>
                                                    <div class="text-truncate" style="max-width: 200px;" title="@acquittal.Application?.Description">
                                                        @acquittal.Application?.Description
                                                    </div>
                                                </td>
                                                <td><strong>$@disbursedAmount.ToString("N2")</strong></td>
                                                <td><strong>$@acquittedAmount.ToString("N2")</strong></td>
                                                <td>
                                                    <strong class="@(outstanding > 0 ? "text-danger" : "text-success")">
                                                        $@outstanding.ToString("N2")
                                                    </strong>
                                                </td>
                                                <td>
                                                    <span class="status-indicator <EMAIL>()">@statusText</span>
                                                </td>
                                                <td>
                                                    @if (acquittal.Application?.DateDisbursed.HasValue == true)
                                                    {
                                                        @acquittal.Application.DateDisbursed.Value.ToString("MMM dd, yyyy")
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">Not disbursed</span>
                                                    }
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-5">
                                <i class="fas fa-receipt fa-4x text-muted mb-3"></i>
                                <h6 class="text-muted">No acquittals found</h6>
                                <p class="text-muted">No acquittals match your search criteria.</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Submissions -->
        @if (Model.AcquittalSubmissions.Any())
        {
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Recent Acquittal Submissions</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-container">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Submission ID</th>
                                            <th>Application ID</th>
                                            <th>Company</th>
                                            <th>Amount</th>
                                            <th>Description</th>
                                            <th>Date Submitted</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var submission in Model.AcquittalSubmissions.OrderByDescending(s => s.Date).Take(20))
                                        {
                                            <tr>
                                                <td><strong>#@submission.SubmissionID</strong></td>
                                                <td><strong>#@submission.Acquittal?.Application?.ApplicationID</strong></td>
                                                <td>@submission.Acquittal?.Application?.Company?.Name</td>
                                                <td><strong>$@submission.Amount.ToString("N2")</strong></td>
                                                <td>
                                                    <div class="text-truncate" style="max-width: 200px;" title="@submission.Description">
                                                        @submission.Description
                                                    </div>
                                                </td>
                                                <td>@submission.Date.ToString("MMM dd, yyyy")</td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>
</div>
