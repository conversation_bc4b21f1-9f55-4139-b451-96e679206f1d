using _Cashdisbursment_.Models;
using _Cashdisbursment_.Services;
using _Cashdisbursment_.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace _Cashdisbursment_.Pages.Approvers
{
    public class ApplicationsModel : PageModel
    {
        private readonly ApplicationService _applicationService;
        private readonly ApprovalService _approvalService;
        private readonly CompanyService _companyService;

        public ApplicationsModel(ApplicationService applicationService, ApprovalService approvalService, CompanyService companyService)
        {
            _applicationService = applicationService;
            _approvalService = approvalService;
            _companyService = companyService;
        }

        public User? CurrentUser { get; set; }
        public int ApproverLevel { get; set; }
        public List<Application> Applications { get; set; } = new();
        public List<Models.Company> Companies { get; set; } = new();

        // Filter properties with proper binding
        [BindProperty(SupportsGet = true)]
        public string? SearchTerm { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? StatusFilter { get; set; }

        [BindProperty(SupportsGet = true)]
        public int? CompanyFilter { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? DateRange { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? ApprovalFilter { get; set; }

        // Statistics
        public int TotalApplications { get; set; }
        public int PendingForApproval { get; set; }
        public int ApprovedByMe { get; set; }
        public int RejectedByMe { get; set; }

        [TempData]
        public string? SuccessMessage { get; set; }

        [TempData]
        public string? ErrorMessage { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            // Redirect approvers to admin applications page
            return RedirectToPage("/Admin/Applications");
        }

            try
            {
                // Get approver level
                var approvers = await _approvalService.GetAllApproversAsync();
                var currentApprover = approvers.FirstOrDefault(a => a.Email == CurrentUser.Email);
                
                if (currentApprover == null)
                {
                    ErrorMessage = "You are not configured as an approver. Please contact the administrator.";
                    return Page();
                }

                ApproverLevel = currentApprover.ApproverNum;

                // Load companies for filter dropdown
                Companies = await _companyService.GetAllCompaniesAsync();

                // Load applications visible to this approver
                var allApplications = await _applicationService.GetApplicationsVisibleToApproverAsync(ApproverLevel);
                
                // Apply filters
                Applications = FilterApplications(allApplications);

                // Calculate statistics
                CalculateStatistics(allApplications);

                return Page();
            }
            catch (Exception ex)
            {
                ErrorMessage = "An error occurred while loading applications. Please try again.";
                return Page();
            }
        }

        private List<Application> FilterApplications(List<Application> applications)
        {
            var filtered = applications.AsEnumerable();

            // Search filter
            if (!string.IsNullOrEmpty(SearchTerm))
            {
                filtered = filtered.Where(a => 
                    a.Description.Contains(SearchTerm, StringComparison.OrdinalIgnoreCase) ||
                    a.Purpose.Contains(SearchTerm, StringComparison.OrdinalIgnoreCase) ||
                    (a.Company?.Name.Contains(SearchTerm, StringComparison.OrdinalIgnoreCase) ?? false));
            }

            // Status filter
            if (!string.IsNullOrEmpty(StatusFilter))
            {
                filtered = StatusFilter.ToLower() switch
                {
                    "pending" => filtered.Where(a => a.Status == "Pending"),
                    "approved" => filtered.Where(a => a.Status == "Approved"),
                    "disbursed" => filtered.Where(a => a.IsDisbursed),
                    "rejected" => filtered.Where(a => a.Status == "Rejected"),
                    "pending-for-me" => filtered.Where(a => a.Status == "Pending" && a.ApprovalLevel == ApproverLevel - 1),
                    _ => filtered
                };
            }

            // Company filter
            if (CompanyFilter.HasValue)
            {
                filtered = filtered.Where(a => a.CompanyID == CompanyFilter.Value);
            }

            // Date range filter
            if (!string.IsNullOrEmpty(DateRange) && int.TryParse(DateRange, out int days))
            {
                var cutoffDate = DateTime.Now.AddDays(-days);
                filtered = filtered.Where(a => a.DateRequested >= cutoffDate);
            }

            // Approval filter
            if (!string.IsNullOrEmpty(ApprovalFilter))
            {
                filtered = ApprovalFilter.ToLower() switch
                {
                    "my-level" => filtered.Where(a => a.ApprovalLevel >= ApproverLevel - 1),
                    "below-my-level" => filtered.Where(a => a.ApprovalLevel < ApproverLevel - 1),
                    "above-my-level" => filtered.Where(a => a.ApprovalLevel > ApproverLevel),
                    _ => filtered
                };
            }

            return filtered.OrderByDescending(a => a.DateRequested).ToList();
        }

        private void CalculateStatistics(List<Application> allApplications)
        {
            TotalApplications = allApplications.Count;
            PendingForApproval = allApplications.Count(a => a.Status == "Pending" && a.ApprovalLevel == ApproverLevel - 1);
            ApprovedByMe = allApplications.Count(a => a.Status == "Approved" && a.ApprovalLevel >= ApproverLevel);
            RejectedByMe = allApplications.Count(a => a.Status == "Rejected" && a.ApprovalLevel >= ApproverLevel - 1);
        }

        public async Task<IActionResult> OnPostApproveAsync(int id, string? comment)
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);

            if (CurrentUser == null || !AuthUtility.IsApprover(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            try
            {
                // Get approver level
                var approvers = await _approvalService.GetAllApproversAsync();
                var currentApprover = approvers.FirstOrDefault(a => a.Email == CurrentUser.Email);

                if (currentApprover == null)
                {
                    ErrorMessage = "You are not configured as an approver.";
                    return RedirectToPage();
                }

                ApproverLevel = currentApprover.ApproverNum;

                // Get the application
                var application = await _applicationService.GetApplicationByIdAsync(id);
                if (application == null)
                {
                    ErrorMessage = "Application not found.";
                    return RedirectToPage();
                }

                // Check if this approver can approve this application
                if (application.ApprovalLevel != ApproverLevel - 1 || application.Status != "Pending")
                {
                    ErrorMessage = "You cannot approve this application at this time.";
                    return RedirectToPage();
                }

                // Check if there are more approvers after this level
                var maxApprovalLevel = await _approvalService.GetMaxApprovalLevelAsync();
                var newApprovalLevel = ApproverLevel;
                var newStatus = ApproverLevel >= maxApprovalLevel ? "Approved" : "Pending";

                // Update the application
                var success = await _applicationService.UpdateApplicationApprovalAsync(id, newApprovalLevel, newStatus, comment);

                if (success)
                {
                    SuccessMessage = $"Application #{id} has been approved successfully.";
                }
                else
                {
                    ErrorMessage = "Failed to approve the application. Please try again.";
                }

                return RedirectToPage();
            }
            catch (Exception ex)
            {
                ErrorMessage = "An error occurred while approving the application.";
                return RedirectToPage();
            }
        }

        public async Task<IActionResult> OnPostRejectAsync(int id, string? comment)
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);

            if (CurrentUser == null || !AuthUtility.IsApprover(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            try
            {
                // Get approver level
                var approvers = await _approvalService.GetAllApproversAsync();
                var currentApprover = approvers.FirstOrDefault(a => a.Email == CurrentUser.Email);

                if (currentApprover == null)
                {
                    ErrorMessage = "You are not configured as an approver.";
                    return RedirectToPage();
                }

                ApproverLevel = currentApprover.ApproverNum;

                // Get the application
                var application = await _applicationService.GetApplicationByIdAsync(id);
                if (application == null)
                {
                    ErrorMessage = "Application not found.";
                    return RedirectToPage();
                }

                // Check if this approver can reject this application
                if (application.ApprovalLevel != ApproverLevel - 1 || application.Status != "Pending")
                {
                    ErrorMessage = "You cannot reject this application at this time.";
                    return RedirectToPage();
                }

                // Update the application
                var success = await _applicationService.UpdateApplicationApprovalAsync(id, ApproverLevel, "Rejected", comment);

                if (success)
                {
                    SuccessMessage = $"Application #{id} has been rejected.";
                }
                else
                {
                    ErrorMessage = "Failed to reject the application. Please try again.";
                }

                return RedirectToPage();
            }
            catch (Exception ex)
            {
                ErrorMessage = "An error occurred while rejecting the application.";
                return RedirectToPage();
            }
        }
    }
}
