using _Cashdisbursment_.Models;
using _Cashdisbursment_.Services;
using _Cashdisbursment_.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.ComponentModel.DataAnnotations;

namespace _Cashdisbursment_.Pages.Account
{
    public class LoginModel : PageModel
    {
        private readonly UserService _userService;
        private readonly ApprovalService _approvalService;

        public LoginModel(UserService userService, ApprovalService approvalService)
        {
            _userService = userService;
            _approvalService = approvalService;
        }

        [BindProperty]
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [BindProperty]
        [Required]
        [DataType(DataType.Password)]
        public string Password { get; set; } = string.Empty;

        [BindProperty]
        public bool RememberMe { get; set; }

        [TempData]
        public string? ErrorMessage { get; set; }

        [TempData]
        public string? SuccessMessage { get; set; }

        public IActionResult OnGet()
        {
            // Redirect if already authenticated
            if (AuthUtility.IsAuthenticated(HttpContext.Session))
            {
                var user = AuthUtility.GetCurrentUser(HttpContext.Session);
                if (user != null)
                {
                    if (AuthUtility.IsAdminOrApprover(HttpContext.Session))
                        return RedirectToPage("/Admin/Dashboard");
                    else
                        return RedirectToPage("/Company/Dashboard");
                }
            }

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            try
            {
                var user = await _userService.ValidateUserAsync(Email, Password);
                
                if (user == null)
                {
                    ErrorMessage = "Invalid email or password.";
                    return Page();
                }

                if (!user.Status)
                {
                    ErrorMessage = "Your account has been deactivated. Please contact the administrator.";
                    return Page();
                }

                // Set session
                AuthUtility.SetCurrentUser(HttpContext.Session, user);

                // Set approver level if user is an approver or admin with approval rights
                if (user.Role == "Approver" || user.Role == "Admin" || user.Role == "SuperAdmin")
                {
                    var approvers = await _approvalService.GetAllApproversAsync();
                    var approver = approvers.FirstOrDefault(a => a.Email.Equals(user.Email, StringComparison.OrdinalIgnoreCase));
                    if (approver != null)
                    {
                        AuthUtility.SetApproverLevel(HttpContext.Session, approver.ApproverNum);
                    }
                }

                // Redirect based on role - both admins and approvers go to admin dashboard
                if (user.Role == "Admin" || user.Role == "SuperAdmin" || user.Role == "Approver")
                {
                    return RedirectToPage("/Admin/Dashboard");
                }
                else
                {
                    return RedirectToPage("/Company/Dashboard");
                }
            }
            catch (Exception)
            {
                ErrorMessage = "An error occurred during login. Please try again.";
                // Log the exception in a real application
                return Page();
            }
        }
    }
}
