using _Cashdisbursment_.Models;
using System.Text.Json;

namespace _Cashdisbursment_.Utilities
{
    public static class AuthUtility
    {
        private const string SessionKey = "CurrentUser";
        private const string ApproverLevelKey = "ApproverLevel";

        public static void SetCurrentUser(ISession session, User user)
        {
            var userJson = JsonSerializer.Serialize(new
            {
                user.UserID,
                user.Email,
                user.Role,
                user.CompanyID,
                CompanyName = user.Company?.Name
            });
            session.SetString(SessionKey, userJson);
        }

        public static void SetApproverLevel(ISession session, int? approverLevel)
        {
            if (approverLevel.HasValue)
            {
                session.SetInt32(ApproverLevelKey, approverLevel.Value);
            }
            else
            {
                session.Remove(ApproverLevelKey);
            }
        }

        public static User? GetCurrentUser(ISession session)
        {
            var userJson = session.GetString(SessionKey);
            if (string.IsNullOrEmpty(userJson))
                return null;

            try
            {
                var userData = JsonSerializer.Deserialize<JsonElement>(userJson);
                return new User
                {
                    UserID = userData.GetProperty("UserID").GetInt32(),
                    Email = userData.GetProperty("Email").GetString() ?? "",
                    Role = userData.GetProperty("Role").GetString() ?? "",
                    CompanyID = userData.TryGetProperty("CompanyID", out var companyIdProp) && 
                               companyIdProp.ValueKind != JsonValueKind.Null ? 
                               companyIdProp.GetInt32() : null,
                    Company = userData.TryGetProperty("CompanyName", out var companyNameProp) && 
                             companyNameProp.ValueKind != JsonValueKind.Null ?
                             new Company { Name = companyNameProp.GetString() ?? "" } : null
                };
            }
            catch
            {
                return null;
            }
        }

        public static int? GetApproverLevel(ISession session)
        {
            return session.GetInt32(ApproverLevelKey);
        }

        public static void ClearCurrentUser(ISession session)
        {
            session.Remove(SessionKey);
            session.Remove(ApproverLevelKey);
        }

        public static bool IsAuthenticated(ISession session)
        {
            return GetCurrentUser(session) != null;
        }

        public static bool IsAdmin(ISession session)
        {
            var user = GetCurrentUser(session);
            return user != null && (user.Role == "Admin" || user.Role == "SuperAdmin");
        }

        public static bool IsSuperAdmin(ISession session)
        {
            var user = GetCurrentUser(session);
            return user != null && user.Role == "SuperAdmin";
        }

        public static bool IsCompanyUser(ISession session)
        {
            var user = GetCurrentUser(session);
            return user != null && user.Role == "Company";
        }

        public static bool IsApprover(ISession session)
        {
            var user = GetCurrentUser(session);
            return user != null && user.Role == "Approver";
        }

        public static bool IsAdminOrApprover(ISession session)
        {
            var user = GetCurrentUser(session);
            return user != null && (user.Role == "Admin" || user.Role == "SuperAdmin" || user.Role == "Approver");
        }

        public static bool IsAdminWithApprovalRights(ISession session)
        {
            var user = GetCurrentUser(session);
            if (user == null) return false;

            // Only admins who are also designated as approvers can approve
            if (user.Role == "Admin" || user.Role == "SuperAdmin")
            {
                return GetApproverLevel(session).HasValue;
            }

            return false;
        }

        public static bool CanApprove(ISession session)
        {
            var user = GetCurrentUser(session);
            if (user == null) return false;

            // Approvers can always approve
            if (user.Role == "Approver") return true;

            // Admins can only approve if they are also designated as approvers
            if (user.Role == "Admin" || user.Role == "SuperAdmin")
            {
                return GetApproverLevel(session).HasValue;
            }

            return false;
        }

        public static bool HasApprovalAccess(ISession session)
        {
            return CanApprove(session);
        }

        public static bool CanAccessAdminPages(ISession session)
        {
            return IsAdminOrApprover(session);
        }

        public static bool CanManageUsers(ISession session)
        {
            return IsAdmin(session); // Only admins can manage users, not approvers
        }

        public static bool CanManageCompanies(ISession session)
        {
            return IsAdmin(session); // Only admins can manage companies, not approvers
        }

        public static bool CanManageApprovers(ISession session)
        {
            return IsAdmin(session); // Only admins can manage approvers, not approvers themselves
        }

        public static bool CanViewApplications(ISession session)
        {
            return IsAdminOrApprover(session);
        }

        public static bool CanViewAcquittals(ISession session)
        {
            return IsAdminOrApprover(session);
        }

        public static bool HasCompanyAccess(ISession session, int companyId)
        {
            var user = GetCurrentUser(session);
            if (user == null) return false;

            // Admins and Approvers can access all companies
            if (IsAdminOrApprover(session)) return true;

            // Company users can only access their own company
            return user.CompanyID == companyId;
        }
    }
}
